
export interface Product {
  id: string;
  name: string;
  category: string;
  sku: string;
  price: number;
  stock: number;
  description?: string;
  createdAt?: string;
  threshold?: number; // Added threshold property for low stock calculations
}

export interface Sale {
  id: string;
  date: string;
  customer: string;
  items: number;
  amount: number;
  status: 'completed' | 'pending' | 'canceled';
  products: SaleItem[];
  timestamp?: any; // Added timestamp as optional property
  paymentMethod: string;
}

export interface SaleItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
}

export interface Staff {
  id: string;
  name: string;
  email: string;
  role: string;
  performance: number;
  status: 'active' | 'inactive';
}

export interface FirestoreUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  role: 'admin' | 'shopkeeper';
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  author: string;
  timestamp: string;
  isActive: boolean;
}

export interface Expense {
  id: string;
  title: string;
  description?: string;
  amount: number;
  category: string;
  date: string;
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'check' | 'other';
  vendor?: string;
  receiptNumber?: string;
  isRecurring: boolean;
  recurringFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
  createdBy: string; // User ID who created the expense
  status: 'pending' | 'approved' | 'rejected';
}
