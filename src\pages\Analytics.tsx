import { useState, useEffect } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  LineChart, 
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  Scatter<PERSON>hart,
  Scatter
} from "recharts";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/common/DataTable";
import { 
  ArrowDownToLine, 
  Calendar, 
  Filter, 
  LineChart as LineChartIcon,
  Printer, 
  Settings
} from "lucide-react";
import { 
  collection, 
  getDocs,
  query,
  where,
  Timestamp,
  orderBy,
  limit
} from 'firebase/firestore';
import { firebaseFirestore } from "@/lib/firebase"; // Updated import
import { Product, Sale } from "@/types/firebase";
import { format, subDays, subMonths, parseISO, startOfMonth, endOfMonth, isAfter, isBefore } from "date-fns";
import { toast } from "sonner";

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export default function Analytics() {
  const [selectedTab, setSelectedTab] = useState("overview");
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch data from Firestore
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch sales data
        const salesRef = collection(firebaseFirestore, "sales");
        const salesQuery = query(
          salesRef,
          orderBy("timestamp", "desc"),
          limit(100) // Limit to last 100 sales for performance
        );
        const salesSnapshot = await getDocs(salesQuery);
        const fetchedSales = salesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Sale[];
        
        // Fetch products data
        const productsRef = collection(firebaseFirestore, "products");
        const productsSnapshot = await getDocs(productsRef);
        const fetchedProducts = productsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Product[];
        
        setSales(fetchedSales);
        setProducts(fetchedProducts);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load analytics data. Please try again.");
        toast.error("Failed to load analytics data");
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Function to parse dates from sales data
  const getSaleDate = (sale: Sale) => {
    if (sale.timestamp && typeof sale.timestamp.toDate === 'function') {
      return sale.timestamp.toDate();
    } else if (sale.date) {
      try {
        const date = parseISO(sale.date);
        if (!isNaN(date.getTime())) return date;
      } catch (error) {
        console.error("Invalid date format:", sale.date);
      }
    }
    return null; // Invalid or missing date
  };
  
  // Get daily store traffic data (approximated from sales)
  const getDailyTrafficData = () => {
    if (sales.length === 0) return [];
    
    // Initialize with hourly buckets
    const hours = ['8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', 
                  '3 PM', '4 PM', '5 PM', '6 PM', '7 PM', '8 PM', '9 PM'];
    const hourMap = new Map<string, number>();
    
    // Initialize the map with zeros
    hours.forEach(hour => {
      hourMap.set(hour, 0);
    });
    
    // Count sales by hour
    sales.forEach(sale => {
      const date = getSaleDate(sale);
      if (date) {
        const hour = format(date, 'h a');
        if (hourMap.has(hour)) {
          const currentValue = hourMap.get(hour);
          if (currentValue !== undefined) {
            hourMap.set(hour, currentValue + 1);
          }
        }
      }
    });
    
    // Convert to array for the chart
    return hours.map(hour => ({
      time: hour,
      visitors: (hourMap.get(hour) || 0) * 3 // Multiplying by 3 to estimate total visitors from sales
    }));
  };
  
  // Get revenue trend data for last 30 days
  const getRevenueTrendData = () => {
    if (sales.length === 0) return [];
    
    const dailyRevenue = new Map<string, number>();
    
    // Initialize days
    for (let i = 29; i >= 0; i--) {
      const day = format(subDays(new Date(), i), 'd');
      dailyRevenue.set(day, 0);
    }
    
    // Sum revenue by day
    sales.forEach(sale => {
      const date = getSaleDate(sale);
      if (date && isAfter(date, subDays(new Date(), 30)) && isBefore(date, new Date())) {
        const day = format(date, 'd');
        if (dailyRevenue.has(day)) {
          const currentValue = dailyRevenue.get(day);
          if (currentValue !== undefined) {
            dailyRevenue.set(day, currentValue + sale.amount);
          }
        }
      }
    });
    
    // Convert to array for the chart
    return Array.from(dailyRevenue.entries()).map(([day, revenue]) => ({
      day,
      revenue
    }));
  };
  
  // Get category performance data
  const getCategoryPerformanceData = () => {
    if (sales.length === 0 || products.length === 0) return [];
    
    const categorySales = new Map<string, number>();
    const categoryProfit = new Map<string, number>();
    
    // Initialize categories
    products.forEach(product => {
      const category = product.category || "Uncategorized";
      if (!categorySales.has(category)) {
        categorySales.set(category, 0);
        categoryProfit.set(category, 0);
      }
    });
    
    // Calculate sales and profit by category
    sales.forEach(sale => {
      sale.products?.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          const category = product.category || "Uncategorized";
          const saleAmount = item.quantity * item.price;
          
          const currentSales = categorySales.get(category);
          if (currentSales !== undefined) {
            categorySales.set(category, currentSales + saleAmount);
          }
          
          // Simplified profit calculation (30% of sale)
          const currentProfit = categoryProfit.get(category);
          if (currentProfit !== undefined) {
            categoryProfit.set(category, currentProfit + (saleAmount * 0.3));
          }
        }
      });
    });
    
    // Convert to array for the chart
    return Array.from(categorySales.keys()).map(name => ({
      name,
      sales: Math.round(categorySales.get(name) || 0),
      profit: Math.round(categoryProfit.get(name) || 0)
    })).sort((a, b) => b.sales - a.sales).slice(0, 5); // Top 5 categories
  };
  
  // Get customer segment data
  const getCustomerSegmentData = () => {
    if (sales.length === 0) return [];
    
    // Group customers by frequency
    const customerFrequency = new Map<string, number>();
    
    sales.forEach(sale => {
      if (sale.customer) {
        const currentFreq = customerFrequency.get(sale.customer) || 0;
        customerFrequency.set(sale.customer, currentFreq + 1);
      }
    });
    
    // Count customers by segment
    let newCount = 0;        // 1 purchase
    let returningCount = 0;  // 2-5 purchases
    let loyalCount = 0;      // 6+ purchases
    
    customerFrequency.forEach((count) => {
      if (count === 1) newCount++;
      else if (count >= 2 && count <= 5) returningCount++;
      else loyalCount++;
    });
    
    // Calculate percentages
    const total = newCount + returningCount + loyalCount;
    if (total === 0) return [
      { name: "No Data", value: 100 }
    ];
    
    return [
      { name: "New", value: Math.round((newCount / total) * 100) },
      { name: "Returning", value: Math.round((returningCount / total) * 100) },
      { name: "Loyal", value: Math.round((loyalCount / total) * 100) }
    ];
  };
  
  // Get key products performance
  const getKeyProductsData = () => {
    if (sales.length === 0 || products.length === 0) return [];
    
    const productSales = new Map<string, number>();
    const productRevenue = new Map<string, number>();
    
    // Calculate sales and revenue by product
    sales.forEach(sale => {
      sale.products?.forEach(item => {
        const currentSales = productSales.get(item.productId) || 0;
        productSales.set(item.productId, currentSales + item.quantity);
        
        const currentRevenue = productRevenue.get(item.productId) || 0;
        productRevenue.set(item.productId, currentRevenue + (item.quantity * item.price));
      });
    });
    
    // Create product performance data
    const productPerformance = products.map(product => {
      const salesCount = productSales.get(product.id) || 0;
      const revenue = productRevenue.get(product.id) || 0;
      
      // Calculate growth based on previous period
      const previousPeriodSales = salesCount * 0.8; // Simplified for demo
      const growth = Math.round(((salesCount - previousPeriodSales) / previousPeriodSales) * 100);
      
      return {
        id: product.id,
        name: product.name,
        sales: salesCount,
        revenue,
        growth
      };
    });
    
    // Return top 8 products by revenue
    return productPerformance
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 8);
  };
  
  // Calculate metrics
  const getTotalRevenue = () => {
    return sales.reduce((total, sale) => total + sale.amount, 0).toFixed(2);
  };
  
  const getSalesCount = () => {
    return sales.length;
  };
  
  const getAverageOrderValue = () => {
    if (sales.length === 0) return "0.00";
    return (sales.reduce((total, sale) => total + sale.amount, 0) / sales.length).toFixed(2);
  };
  
  const getCustomerCount = () => {
    const uniqueCustomers = new Set();
    sales.forEach(sale => {
      if (sale.customer) uniqueCustomers.add(sale.customer);
    });
    return uniqueCustomers.size;
  };
  
  // Process data for UI
  const dailyTrafficData = getDailyTrafficData();
  const customerSegments = getCustomerSegmentData();
  const categoryPerformance = getCategoryPerformanceData();
  const keyProducts = getKeyProductsData();
  const revenueTrend = getRevenueTrendData();

  if (error) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="text-center space-y-4">
          <p className="text-red-500">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Store Analytics</h1>
        <p className="text-muted-foreground">
          Detailed insights into your stationery shop's sales performance and customer trends.
        </p>
      </div>
      
      <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
          </TabsList>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Last 30 Days
            </Button>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <ArrowDownToLine className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Printer className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {loading ? "..." : getTotalRevenue()}</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  <span className="text-green-500 font-medium mr-1">↑ 12%</span> from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Sales Count</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? "..." : getSalesCount()}</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  <span className="text-green-500 font-medium mr-1">↑ 8%</span> from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {loading ? "..." : getAverageOrderValue()}</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  <span className="text-green-500 font-medium mr-1">↑ 3%</span> from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Customer Count</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? "..." : getCustomerCount()}</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  <span className="text-green-500 font-medium mr-1">↑ 5%</span> from last month
                </p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Revenue Trend</CardTitle>
                  <CardDescription>Daily revenue for the last month</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  Details
                </Button>
              </div>
            </CardHeader>
            <CardContent className="h-80">
              {loading ? (
                <div className="flex h-full items-center justify-center">
                  <p>Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={revenueTrend}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis 
                      dataKey="day" 
                      tickFormatter={(value) => (parseInt(value) % 5 === 0 || value === "1" || value === "30") ? value : ""}
                    />
                    <YAxis />
                    <Tooltip 
                      formatter={(value) => [`Tsh${value}`, "Revenue"]}
                      contentStyle={{
                        backgroundColor: "white",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                        border: "none",
                      }}
                    />
                    <Area type="monotone" dataKey="revenue" stroke="hsl(var(--primary))" fill="hsl(var(--primary)/.2)" />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Daily Store Traffic</CardTitle>
                <CardDescription>
                  Hourly visitor count for today
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {loading ? (
                  <div className="flex h-full items-center justify-center">
                    <p>Loading data...</p>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={dailyTrafficData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "white",
                          borderRadius: "8px",
                          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                          border: "none",
                        }}
                      />
                      <Bar dataKey="visitors" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Customer Segments</CardTitle>
                <CardDescription>
                  Distribution of customer types
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {loading ? (
                  <div className="flex h-full items-center justify-center">
                    <p>Loading data...</p>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={customerSegments}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {customerSegments.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, "Percentage"]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="sales" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales by Category</CardTitle>
              <CardDescription>
                Performance comparison across product categories
              </CardDescription>
            </CardHeader>
            <CardContent className="h-96">
              {loading ? (
                <div className="flex h-full items-center justify-center">
                  <p>Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={categoryPerformance}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [`Tsh${value}`, "Amount"]}
                      contentStyle={{
                        backgroundColor: "white",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                        border: "none",
                      }}
                    />
                    <Legend />
                    <Bar dataKey="sales" name="Sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="profit" name="Profit" fill="#22c55e" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="customers" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Acquisition</CardTitle>
              <CardDescription>
                New customers over time
              </CardDescription>
            </CardHeader>
            <CardContent className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={[
                    { month: "Jan", newCustomers: 65, returningCustomers: 40 },
                    { month: "Feb", newCustomers: 75, returningCustomers: 45 },
                    { month: "Mar", newCustomers: 85, returningCustomers: 55 },
                    { month: "Apr", newCustomers: 80, returningCustomers: 60 },
                    { month: "May", newCustomers: 95, returningCustomers: 65 },
                    { month: "Jun", newCustomers: 110, returningCustomers: 70 },
                    { month: "Jul", newCustomers: 120, returningCustomers: 75 },
                    { month: "Aug", newCustomers: 115, returningCustomers: 80 },
                    { month: "Sep", newCustomers: 130, returningCustomers: 85 },
                    { month: "Oct", newCustomers: 140, returningCustomers: 90 },
                    { month: "Nov", newCustomers: 150, returningCustomers: 95 },
                    { month: "Dec", newCustomers: 160, returningCustomers: 100 },
                  ]}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "white",
                      borderRadius: "8px",
                      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                      border: "none",
                    }}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="newCustomers" name="New Customers" stroke="hsl(var(--primary))" strokeWidth={2} />
                  <Line type="monotone" dataKey="returningCustomers" name="Returning Customers" stroke="#22c55e" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="products" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Key Products Performance</CardTitle>
                  <CardDescription>
                    Sales and revenue analysis of top products
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Customize
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex h-40 items-center justify-center">
                  <p>Loading data...</p>
                </div>
              ) : (
                <DataTable
                  data={keyProducts}
                  columns={[
                    {
                      id: "name",
                      header: "Product",
                      cell: (row) => <div className="font-medium">{row.name}</div>,
                    },
                    {
                      id: "sales",
                      header: "Units Sold",
                      cell: (row) => <div>{row.sales}</div>,
                    },
                    {
                      id: "revenue",
                      header: "Revenue",
                      cell: (row) => <div className="font-medium">${row.revenue.toLocaleString()}</div>,
                    },
                    {
                      id: "growth",
                      header: "Growth",
                      cell: (row) => (
                        <div className={`flex items-center ${row.growth >= 0 ? "text-green-500" : "text-red-500"}`}>
                          {row.growth >= 0 ? "↑" : "↓"} {Math.abs(row.growth)}%
                        </div>
                      ),
                    },
                  ]}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}