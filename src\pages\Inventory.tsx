
import { useState } from "react";
import {
  Search,
  Plus,
  Download,
  Upload,
  Package,
  Filter,
  Edit,
  Trash2,
  MoreH<PERSON>zontal,
  Check,
  X
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogTrigger, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { AddProductForm } from "@/components/inventory/AddProductForm";
import { useQuery } from "@tanstack/react-query";
import { firestore } from "@/lib/firebase";
import type { Product } from '@/types/firebase';
import { getFirestore, collection, getDocs, doc, updateDoc, deleteDoc } from "firebase/firestore";

export default function Inventory() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [sortOrder, setSortOrder] = useState("name-asc");
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [editForm, setEditForm] = useState({
    name: "",
    category: "",
    price: "",
    stock: "",
    sku: "",
    description: ""
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const { toast } = useToast();
  const { isAdmin } = useAuth();

  const { data: productsData = [], isLoading, refetch } = useQuery<Product[]>({
    queryKey: ["products"],
    queryFn: async () => {
      // Direct Firestore access for more reliable fetching
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      const querySnapshot = await getDocs(productsCollection);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Product, 'id'>)
      }));
    }
  });

  const inventoryData = productsData;

  const filteredData = inventoryData.filter((item: any) => {
    const matchesSearch = item.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         item.sku?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "All Categories" || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const sortedData = [...filteredData].sort((a: any, b: any) => {
    const [field, direction] = sortOrder.split("-");
    
    if (field === "name") {
      return direction === "asc" 
        ? a.name?.localeCompare(b.name) 
        : b.name?.localeCompare(a.name);
    } else if (field === "price") {
      return direction === "asc" 
        ? a.price - b.price 
        : b.price - a.price;
    } else if (field === "stock") {
      return direction === "asc" 
        ? a.stock - b.stock 
        : b.stock - a.stock;
    }
    return 0;
  });

  const outOfStock = inventoryData.filter((item: any) => item.stock === 0).length;
  const lowStock = inventoryData.filter((item: any) => item.stock > 0 && item.stock <= 5).length;
  const inStock = inventoryData.filter((item: any) => item.stock > 5).length;

  const categoriesSet = new Set(["All Categories"]);
  inventoryData.forEach((item: any) => {
    if (item.category) categoriesSet.add(item.category);
  });
  const categories = Array.from(categoriesSet);

  const handleExportCSV = () => {
    const headers = ["Name", "Category", "SKU", "Price", "Stock"];
    const csvRows = [headers.join(",")];
    
    for (const item of sortedData) {
      const values = [
        `"${item.name || ''}"`,
        `"${item.category || ''}"`,
        `"${item.sku || ''}"`,
        `${item.price || 0}`,
        `${item.stock || 0}`
      ];
      csvRows.push(values.join(","));
    }
    
    const csvString = csvRows.join("\n");
    
    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    
    link.setAttribute("href", url);
    link.setAttribute("download", "inventory.csv");
    link.style.visibility = "hidden";
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Export Successful",
      description: `${sortedData.length} products exported to CSV`
    });
  };

  // Edit and Delete functions
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setEditForm({
      name: product.name,
      category: product.category,
      price: product.price.toString(),
      stock: product.stock.toString(),
      sku: product.sku,
      description: product.description || ""
    });
  };

  const handleUpdateProduct = async () => {
    if (!editingProduct || !editForm.name.trim() || !editForm.category || !editForm.price || !editForm.stock || !editForm.sku.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (isNaN(Number(editForm.price)) || isNaN(Number(editForm.stock))) {
      toast({
        title: "Error",
        description: "Price and stock must be valid numbers",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUpdating(true);
      const db = getFirestore();
      const productRef = doc(db, "products", editingProduct.id);

      await updateDoc(productRef, {
        name: editForm.name,
        category: editForm.category,
        price: parseFloat(editForm.price),
        stock: parseInt(editForm.stock),
        sku: editForm.sku,
        description: editForm.description,
        updatedAt: new Date().toISOString()
      });

      toast({
        title: "Success",
        description: "Product updated successfully",
      });

      setEditingProduct(null);
      setEditForm({ name: "", category: "", price: "", stock: "", sku: "", description: "" });
      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update product",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    try {
      setIsDeleting(productId);
      const db = getFirestore();
      const productRef = doc(db, "products", productId);

      await deleteDoc(productRef);

      toast({
        title: "Success",
        description: "Product deleted successfully",
      });

      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete product",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Stationery Inventory</h1>
        <p className="text-muted-foreground">
          Manage your stationery products, monitor stock levels, and track office supplies inventory.
        </p>
      </div>

      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              In Stock
            </CardTitle>
            <div className="h-4 w-4 text-green-500">
              <Package size={16} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{inStock}</div>
            <p className="text-xs text-muted-foreground">
              Stationery items with good stock levels
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Low Stock
            </CardTitle>
            <div className="h-4 w-4 text-amber-500">
              <Package size={16} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{lowStock}</div>
            <p className="text-xs text-muted-foreground">
              Stationery items that need restocking
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Out of Stock
            </CardTitle>
            <div className="h-4 w-4 text-red-500">
              <Package size={16} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{outOfStock}</div>
            <p className="text-xs text-muted-foreground">
              Items completely out of stock
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search stationery items..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2 sm:gap-4">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={sortOrder} onValueChange={setSortOrder}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                  <SelectItem value="price-asc">Price (Low-High)</SelectItem>
                  <SelectItem value="price-desc">Price (High-Low)</SelectItem>
                  <SelectItem value="stock-asc">Stock (Low-High)</SelectItem>
                  <SelectItem value="stock-desc">Stock (High-Low)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              variant="outline"
              size="sm"
              className="w-full sm:w-auto"
              onClick={handleExportCSV}
            >
              <Download className="mr-2 h-4 w-4" />
              <span className="sm:hidden">Export</span>
              <span className="hidden sm:inline">Export CSV</span>
            </Button>
            {isAdmin() && (
              <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
                <DialogTrigger asChild>
                  <Button size="sm" className="w-full sm:w-auto">
                    <Plus className="mr-2 h-4 w-4" />
                    <span className="sm:hidden">Add</span>
                    <span className="hidden sm:inline">Add Product</span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[90vw] md:max-w-[600px] max-h-[90vh] overflow-y-auto">
                  <AddProductForm
                    onClose={() => setShowAddForm(false)}
                    onProductAdded={() => {
                      refetch();
                    }}
                  />
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="whitespace-nowrap">Product</TableHead>
                <TableHead className="whitespace-nowrap hidden sm:table-cell">Category</TableHead>
                <TableHead className="whitespace-nowrap hidden md:table-cell">SKU</TableHead>
                <TableHead className="text-right whitespace-nowrap">Price</TableHead>
                <TableHead className="text-right whitespace-nowrap">Stock</TableHead>
                <TableHead className="text-right whitespace-nowrap">Status</TableHead>
{isAdmin() && <TableHead className="text-right whitespace-nowrap">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={isAdmin() ? 7 : 6} className="h-24 text-center">
                    <div className="flex justify-center">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : sortedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isAdmin() ? 7 : 6} className="h-24 text-center">
                    No products found
                  </TableCell>
                </TableRow>
              ) : (
                sortedData.map((product: any) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">
                      <div className="min-w-0">
                        <div className="truncate">{product.name}</div>
                        <div className="text-xs text-muted-foreground sm:hidden">
                          {product.category} • {product.sku}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">{product.category}</TableCell>
                    <TableCell className="font-mono text-xs hidden md:table-cell">{product.sku}</TableCell>
                    <TableCell className="text-right whitespace-nowrap">Tsh {product.price?.toFixed(2) || '0.00'}</TableCell>
                    <TableCell className="text-right">{product.stock}</TableCell>
                    <TableCell className="text-right">
                      {product.stock === 0 ? (
                        <Badge variant="destructive" className="text-xs">
                          <span className="hidden sm:inline">Out of Stock</span>
                          <span className="sm:hidden">Out</span>
                        </Badge>
                      ) : product.stock <= 5 ? (
                        <Badge variant="outline" className="bg-amber-100 text-amber-700 hover:bg-amber-100 border-amber-200 text-xs">
                          <span className="hidden sm:inline">Low Stock</span>
                          <span className="sm:hidden">Low</span>
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-green-100 text-green-700 hover:bg-green-100 border-green-200 text-xs">
                          <span className="hidden sm:inline">In Stock</span>
                          <span className="sm:hidden">In</span>
                        </Badge>
                      )}
                    </TableCell>
                    {isAdmin() && (
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditProduct(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem
                                  onSelect={(e) => e.preventDefault()}
                                  className="text-red-600 cursor-pointer"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Product</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "{product.name}"? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteProduct(product.id)}
                                    disabled={isDeleting === product.id}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    {isDeleting === product.id ? (
                                      <>
                                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Edit Product Dialog - Admin Only */}
      {isAdmin() && (
        <Dialog open={!!editingProduct} onOpenChange={() => setEditingProduct(null)}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Product</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="edit-name" className="text-sm font-medium">Product Name *</label>
                <Input
                  id="edit-name"
                  value={editForm.name}
                  onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="edit-category" className="text-sm font-medium">Category *</label>
                <Select value={editForm.category} onValueChange={(value) => setEditForm({ ...editForm, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Writing Supplies">Writing Supplies</SelectItem>
                    <SelectItem value="Paper Products">Paper Products</SelectItem>
                    <SelectItem value="Office Supplies">Office Supplies</SelectItem>
                    <SelectItem value="Art Supplies">Art Supplies</SelectItem>
                    <SelectItem value="Storage & Organization">Storage & Organization</SelectItem>
                    <SelectItem value="Technology">Technology</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="edit-price" className="text-sm font-medium">Price (Tsh) *</label>
                <Input
                  id="edit-price"
                  type="number"
                  step="0.01"
                  value={editForm.price}
                  onChange={(e) => setEditForm({ ...editForm, price: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="edit-stock" className="text-sm font-medium">Stock Quantity *</label>
                <Input
                  id="edit-stock"
                  type="number"
                  value={editForm.stock}
                  onChange={(e) => setEditForm({ ...editForm, stock: e.target.value })}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <label htmlFor="edit-sku" className="text-sm font-medium">SKU *</label>
              <Input
                id="edit-sku"
                value={editForm.sku}
                onChange={(e) => setEditForm({ ...editForm, sku: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="edit-description" className="text-sm font-medium">Description</label>
              <textarea
                id="edit-description"
                value={editForm.description}
                onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                rows={3}
                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setEditingProduct(null)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleUpdateProduct}
              disabled={isUpdating || !editForm.name.trim() || !editForm.category || !editForm.price || !editForm.stock || !editForm.sku.trim()}
            >
              {isUpdating ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Update Product
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      )}
    </div>
  );
}
